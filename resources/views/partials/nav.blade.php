<nav class="navbar navbar-expand-lg shadow" style="background-color: #63D1CC;">
    <div class="container">
        <!-- Logo - Always visible -->
        <a class="navbar-brand {{ app()->getLocale() == 'fa' ? 'order-0' : 'order-0' }}" href="{{ route('home') }}" style="padding: 0;">
            <img src="{{ asset('images/logo.png') }}" alt="Lian Taraz Motavazen" height="70">
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav {{ app()->getLocale() == 'fa' ? 'order-1' : 'order-0' }}">
                <li class="nav-item ms-3">
                    <a class="nav-link text-dark fw-bold" href="{{ route('home') }}">{{ __('messages.home_page') }}</a>
                </li>
                <li class="nav-item dropdown ms-3">
                    <a class="nav-link dropdown-toggle text-dark fw-bold" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        {{ __('messages.services') }}
                    </a>
                    <ul class="dropdown-menu {{ app()->getLocale() == 'fa' ? 'dropdown-menu-end text-right' : '' }}" aria-labelledby="servicesDropdown">
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('services') }}">{{ __('messages.our_services') }}</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('services.business') }}">{{ __('messages.business_commercial_services') }}</a></li>
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('services.financial') }}">{{ __('messages.financial_commercial_services') }}</a></li>
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('services.transport') }}">{{ __('messages.domestic_international_transport') }}</a></li>
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('services.investment') }}">{{ __('messages.investment_capital') }}</a></li>
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('services.hr') }}">{{ __('messages.hr_supply') }}</a></li>
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('services.insurance') }}">{{ __('messages.financial_reporting') }}</a></li>
                    </ul>
                </li>
                <!-- Articles Menu -->
                <li class="nav-item dropdown ms-3">
                    <a class="nav-link dropdown-toggle text-dark fw-bold" href="#" id="articlesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        {{ app()->getLocale() == 'fa' ? 'مقالات' : 'Articles' }}
                    </a>
                    <ul class="dropdown-menu {{ app()->getLocale() == 'fa' ? 'dropdown-menu-end text-right' : '' }}" aria-labelledby="articlesDropdown">
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('articles.index') }}">{{ app()->getLocale() == 'fa' ? 'همه مقالات' : 'All Articles' }}</a></li>
                        <li><hr class="dropdown-divider"></li>
                        @php
                            $articleCategories = \App\Models\Category::active()->forType('article')->parents()->ordered()->get();
                        @endphp
                        @foreach($articleCategories as $category)
                            <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('articles.category', $category->slug) }}">{{ $category->name }}</a></li>
                        @endforeach
                    </ul>
                </li>

                <!-- News Menu -->
                <li class="nav-item dropdown ms-3">
                    <a class="nav-link dropdown-toggle text-dark fw-bold" href="#" id="newsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        {{ app()->getLocale() == 'fa' ? 'اخبار' : 'News' }}
                    </a>
                    <ul class="dropdown-menu {{ app()->getLocale() == 'fa' ? 'dropdown-menu-end text-right' : '' }}" aria-labelledby="newsDropdown">
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('news.index') }}">{{ app()->getLocale() == 'fa' ? 'همه اخبار' : 'All News' }}</a></li>
                        <li><hr class="dropdown-divider"></li>
                        @php
                            $newsCategories = \App\Models\Category::active()->forType('news')->parents()->ordered()->get();
                        @endphp
                        @foreach($newsCategories as $category)
                            <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('news.category', $category->slug) }}">{{ $category->name }}</a></li>
                        @endforeach
                    </ul>
                </li>

                <li class="nav-item ms-3">
                    <a class="nav-link text-dark fw-bold" href="{{ route('about') }}">{{ __('messages.about') }}</a>
                </li>
                <li class="nav-item ms-3">
                    <a class="nav-link text-dark fw-bold" href="{{ route('contact') }}">{{ __('messages.contact') }}</a>
                </li>


                <!-- Language Switcher -->
                <li class="nav-item dropdown ms-3">
                    <a class="nav-link dropdown-toggle text-dark fw-bold" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        {{ app()->getLocale() == 'en' ? 'English' : 'فارسی' }}
                    </a>
                    <ul class="dropdown-menu {{ app()->getLocale() == 'fa' ? 'dropdown-menu-end text-right' : '' }}" aria-labelledby="languageDropdown">
                        <li><a class="dropdown-item {{ app()->getLocale() == 'en' ? 'active' : '' }} {{ app()->getLocale() == 'fa' ? 'text-right' : '' }}" href="{{ route('language.switch', 'en') }}">English</a></li>
                        <li><a class="dropdown-item {{ app()->getLocale() == 'fa' ? 'active text-right' : '' }}" href="{{ route('language.switch', 'fa') }}">فارسی</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>
