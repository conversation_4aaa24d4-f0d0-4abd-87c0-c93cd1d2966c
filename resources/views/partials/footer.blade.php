<footer class="py-4 position-relative" style="background-color: #63D1CC; overflow: hidden; color: #333;">
    <!-- SVG Complex Persian Mosque Tile Pattern -->
    <div class="position-absolute w-100 h-100" style="top: 0; left: 0; opacity: 0.25; z-index: 0;">
        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none">
            <defs>
                <pattern id="persian-tile-pattern" x="0" y="0" width="300" height="300" patternUnits="userSpaceOnUse">
                    <!-- Central Geometric Star Pattern (Shamseh) -->
                    <path d="M150,150 L170,120 L200,110 L170,90 L160,60 L140,90 L110,100 L140,120 Z" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M150,150 L130,120 L100,110 L130,90 L140,60 L160,90 L190,100 L160,120 Z" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M150,150 L170,180 L200,190 L170,210 L160,240 L140,210 L110,200 L140,180 Z" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M150,150 L130,180 L100,190 L130,210 L140,240 L160,210 L190,200 L160,180 Z" stroke="#ffffff" stroke-width="1.5" fill="none" />

                    <!-- Inner Flower Detail -->
                    <path d="M150,150 m-20,0 a20,20 0 1,0 40,0 a20,20 0 1,0 -40,0" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M150,130 C160,120 170,125 150,140 C130,125 140,120 150,130" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M150,170 C160,180 170,175 150,160 C130,175 140,180 150,170" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M130,150 C120,160 125,170 140,150 C125,130 120,140 130,150" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M170,150 C180,160 175,170 160,150 C175,130 180,140 170,150" stroke="#ffffff" stroke-width="1" fill="none" />

                    <!-- Complex Eslimi Vines -->
                    <path d="M150,50 C180,50 210,80 210,110 C210,140 180,150 150,150 C120,150 90,140 90,110 C90,80 120,50 150,50" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M150,250 C180,250 210,220 210,190 C210,160 180,150 150,150 C120,150 90,160 90,190 C90,220 120,250 150,250" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M50,150 C50,180 80,210 110,210 C140,210 150,180 150,150 C150,120 140,90 110,90 C80,90 50,120 50,150" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M250,150 C250,180 220,210 190,210 C160,210 150,180 150,150 C150,120 160,90 190,90 C220,90 250,120 250,150" stroke="#ffffff" stroke-width="1.5" fill="none" />

                    <!-- Detailed Floral Elements -->
                    <path d="M90,90 C100,80 110,85 105,95 C100,105 90,100 90,90" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M210,90 C200,80 190,85 195,95 C200,105 210,100 210,90" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M90,210 C100,220 110,215 105,205 C100,195 90,200 90,210" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M210,210 C200,220 190,215 195,205 C200,195 210,200 210,210" stroke="#ffffff" stroke-width="1" fill="none" />

                    <!-- Intricate Corner Patterns -->
                    <path d="M30,30 L50,50 M40,30 L60,50 M30,40 L50,60" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M270,30 L250,50 M260,30 L240,50 M270,40 L250,60" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M30,270 L50,250 M40,270 L60,250 M30,260 L50,240" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M270,270 L250,250 M260,270 L240,250 M270,260 L250,240" stroke="#ffffff" stroke-width="1" fill="none" />

                    <!-- Connecting Arabesque Patterns -->
                    <path d="M0,150 C30,150 40,120 50,100 C60,80 80,60 100,50 C120,40 150,30 150,0" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M300,150 C270,150 260,120 250,100 C240,80 220,60 200,50 C180,40 150,30 150,0" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M0,150 C30,150 40,180 50,200 C60,220 80,240 100,250 C120,260 150,270 150,300" stroke="#ffffff" stroke-width="1.5" fill="none" />
                    <path d="M300,150 C270,150 260,180 250,200 C240,220 220,240 200,250 C180,260 150,270 150,300" stroke="#ffffff" stroke-width="1.5" fill="none" />

                    <!-- Small Decorative Flowers -->
                    <path d="M75,75 m-6,0 a6,6 0 1,0 12,0 a6,6 0 1,0 -12,0" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M225,75 m-6,0 a6,6 0 1,0 12,0 a6,6 0 1,0 -12,0" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M75,225 m-6,0 a6,6 0 1,0 12,0 a6,6 0 1,0 -12,0" stroke="#ffffff" stroke-width="1" fill="none" />
                    <path d="M225,225 m-6,0 a6,6 0 1,0 12,0 a6,6 0 1,0 -12,0" stroke="#ffffff" stroke-width="1" fill="none" />

                    <!-- Delicate Leaf Details -->
                    <path d="M120,70 C130,60 140,65 135,75 C130,85 120,80 120,70" stroke="#ffffff" stroke-width="0.8" fill="none" />
                    <path d="M180,70 C170,60 160,65 165,75 C170,85 180,80 180,70" stroke="#ffffff" stroke-width="0.8" fill="none" />
                    <path d="M120,230 C130,240 140,235 135,225 C130,215 120,220 120,230" stroke="#ffffff" stroke-width="0.8" fill="none" />
                    <path d="M180,230 C170,240 160,235 165,225 C170,215 180,220 180,230" stroke="#ffffff" stroke-width="0.8" fill="none" />
                    <path d="M70,120 C60,130 65,140 75,135 C85,130 80,120 70,120" stroke="#ffffff" stroke-width="0.8" fill="none" />
                    <path d="M70,180 C60,170 65,160 75,165 C85,170 80,180 70,180" stroke="#ffffff" stroke-width="0.8" fill="none" />
                    <path d="M230,120 C240,130 235,140 225,135 C215,130 220,120 230,120" stroke="#ffffff" stroke-width="0.8" fill="none" />
                    <path d="M230,180 C240,170 235,160 225,165 C215,170 220,180 230,180" stroke="#ffffff" stroke-width="0.8" fill="none" />
                </pattern>
            </defs>
            <rect x="0" y="0" width="100%" height="100%" fill="url(#persian-tile-pattern)" />
        </svg>
    </div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="row">
            <div class="col-md-3 mb-4 mb-md-0">
                <h4 class="h5 fw-bold">{{ config('app.name') }}</h4>
                <p>{{ __('messages.professional_accounting_services') }}</p>
            </div>
            <div class="col-md-3 mb-4 mb-md-0">
                <h4 class="h5 fw-bold">{{ __('messages.quick_links') }}</h4>
                <ul class="list-unstyled">
                    <li><a href="{{ route('home') }}" class="text-dark text-decoration-none">{{ __('messages.home_page') }}</a></li>
                    <li><a href="{{ route('services') }}" class="text-dark text-decoration-none">{{ __('messages.services') }}</a></li>
                    <li><a href="{{ route('about') }}" class="text-dark text-decoration-none">{{ __('messages.about') }}</a></li>
                    <li><a href="{{ route('contact') }}" class="text-dark text-decoration-none">{{ __('messages.contact') }}</a></li>
                </ul>
            </div>
            <div class="col-md-3">
                <h4 class="h5 fw-bold">{{ app()->getLocale() == 'fa' ? 'پیوندهای مفید' : 'Useful Links' }}</h4>
                <ul class="list-unstyled">
                    <li><a href="https://www.intamedia.ir/" target="_blank" class="text-dark text-decoration-none">{{ app()->getLocale() == 'fa' ? 'سازمان امور مالیاتی کشور' : 'Tax Affairs Organization' }}</a></li>
                    <li><a href="https://www.moe.gov.ir/" target="_blank" class="text-dark text-decoration-none">{{ app()->getLocale() == 'fa' ? 'وزارت اقتصاد' : 'Ministry of Economy' }}</a></li>
                    <li><a href="https://www.irica.gov.ir/" target="_blank" class="text-dark text-decoration-none">{{ app()->getLocale() == 'fa' ? 'گمرک جمهوری اسلامی ایران' : 'Iran Customs' }}</a></li>
                    <li><a href="https://www.tpo.ir/" target="_blank" class="text-dark text-decoration-none">{{ app()->getLocale() == 'fa' ? 'سازمان توسعه تجارت ایران' : 'Trade Promotion Organization' }}</a></li>
                    <li><a href="https://www.mimt.gov.ir/" target="_blank" class="text-dark text-decoration-none">{{ app()->getLocale() == 'fa' ? 'وزارت صمت' : 'Ministry of MIMT' }}</a></li>
                </ul>
            </div>
            <div class="col-md-3">
                <h4 class="h5 fw-bold">{{ __('messages.contact_info') }}</h4>
                <ul class="list-unstyled">
                    <li>{{ __('messages.address_line1') }}</li>
                    <li>{{ __('messages.address_line2') }}</li>
                    <li>{{ __('messages.address_line3') }}</li>
                    <li dir="ltr">+98 77 9100 2036</li>
                    <li dir="ltr"><EMAIL></li>
                </ul>
            </div>
        </div>
        <hr class="my-4 border-dark">
        <div class="row">
            <div class="col-md-6">
                <p class="mb-0">&copy; {{ date('Y') }} {{ config('app.name') }}. {{ __('messages.all_rights_reserved') }}</p>
            </div>
            <div class="col-md-6 text-end">
                <p class="mb-0">{{ __('messages.developed_by') }} <a href="https://20rang.ir" target="_blank" class="text-dark text-decoration-none fw-bold">بیست رنگ</a></p>
            </div>
        </div>
    </div>
</footer>
