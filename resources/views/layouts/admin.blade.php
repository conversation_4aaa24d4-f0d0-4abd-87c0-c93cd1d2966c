<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'fa' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - {{ app()->getLocale() == 'fa' ? 'پنل مدیریت' : 'Admin Panel' }}</title>

    <!-- DNS prefetch -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">

    <!-- Fonts -->
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">

    <!-- Persian Font -->
    @if(app()->getLocale() == 'fa')
        <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazir-font@v30.1.0/dist/font-face.css" rel="stylesheet" type="text/css" />
    @endif

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom Styles -->
    @vite(['resources/css/app.css'])

    <!-- RTL Fixes -->
    @if(app()->getLocale() == 'fa')
        <link href="{{ asset('css/rtl-fixes.css') }}" rel="stylesheet">
    @endif

    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #63D1CC 100%);
        }
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.25rem;
            transition: all 0.3s ease;
        }
        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 0.5rem;
        }
        html[dir="rtl"] .admin-sidebar .nav-link i {
            margin-right: 0;
            margin-left: 0.5rem;
        }
        html[dir="rtl"] .admin-sidebar .nav-link:hover,
        html[dir="rtl"] .admin-sidebar .nav-link.active {
            transform: translateX(-5px);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar sidebar collapse">
                    <div class="position-sticky pt-3">
                        <!-- Logo -->
                        <div class="text-center mb-4">
                            <a href="{{ route('home') }}" class="text-decoration-none">
                                <img src="{{ asset('images/logo.png') }}" alt="Lian Taraz" height="50" class="mb-2">
                                <h5 class="text-white">{{ app()->getLocale() == 'fa' ? 'پنل مدیریت' : 'Admin Panel' }}</h5>
                            </a>
                        </div>

                        <!-- Navigation -->
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                                    <i class="fas fa-tachometer-alt"></i>
                                    {{ app()->getLocale() == 'fa' ? 'داشبورد' : 'Dashboard' }}
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}" href="{{ route('admin.categories.index') }}">
                                    <i class="fas fa-tags"></i>
                                    {{ app()->getLocale() == 'fa' ? 'دسته‌بندی‌ها' : 'Categories' }}
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.articles.*') ? 'active' : '' }}" href="{{ route('admin.articles.index') }}">
                                    <i class="fas fa-file-alt"></i>
                                    {{ app()->getLocale() == 'fa' ? 'مقالات' : 'Articles' }}
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('admin.news.*') ? 'active' : '' }}" href="{{ route('admin.news.index') }}">
                                    <i class="fas fa-newspaper"></i>
                                    {{ app()->getLocale() == 'fa' ? 'اخبار' : 'News' }}
                                </a>
                            </li>
                            
                            <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                            
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('home') }}" target="_blank">
                                    <i class="fas fa-external-link-alt"></i>
                                    {{ app()->getLocale() == 'fa' ? 'مشاهده سایت' : 'View Site' }}
                                </a>
                            </li>
                            
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('logout') }}"
                                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    <i class="fas fa-sign-out-alt"></i>
                                    {{ app()->getLocale() == 'fa' ? 'خروج' : 'Logout' }}
                                </a>
                                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                    @csrf
                                </form>
                            </li>
                        </ul>
                    </div>
                </nav>

                <!-- Main content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 admin-content">
                    <!-- Header -->
                    <div class="admin-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0">@yield('page-title', app()->getLocale() == 'fa' ? 'پنل مدیریت' : 'Admin Panel')</h4>
                                <small class="text-muted">@yield('page-subtitle', '')</small>
                            </div>
                            <div>
                                <span class="text-muted">{{ app()->getLocale() == 'fa' ? 'خوش آمدید' : 'Welcome' }}, {{ Auth::user()->name }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    @yield('content')
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Scripts -->
    @vite(['resources/js/app.js'])

    @yield('scripts')
</body>
</html>
